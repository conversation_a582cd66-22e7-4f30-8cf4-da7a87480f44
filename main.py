from maix import image, display, app, time, camera
import cv2
import numpy as np
import math
from micu_uart_lib import (
    SimpleUART, micu_printf
)

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)
        
    def detect(self, img):
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        lower_purple = np.array([100, 140, 200])
        upper_purple = np.array([180, 255, 255])
        mask_purple = cv2.inRange(hsv, lower_purple, upper_purple)
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel)
        
        contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        laser_points = []
        
        for cnt in contours_purple:
            rect = cv2.minAreaRect(cnt)
            cx, cy = map(int, rect[0])
            laser_points.append((cx, cy))
            cv2.circle(img, (cx, cy), 3, (255, 0, 255), -1)
            cv2.putText(img, "Laser", (cx-20, cy-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
        
        return img, laser_points

# --------------------------- 圆形轨迹点生成函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    """在校正后的矩形内生成圆形轨迹点"""
    circle_points = []
    cx, cy = center
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

# --------------------------- 串口数据包发送函数 ---------------------------
def send_laser_diff_packet(diff_x, diff_y):
    """
    发送激光点与圆心坐标差的二进制数据包
    数据包格式：0x78, 标识符, x高八位, x低八位, y高八位, y低八位, 0xFC

    :param diff_x: x坐标差值（有符号整数）
    :param diff_y: y坐标差值（有符号整数）
    """
    # 将有符号整数转换为无符号16位整数（补码表示）
    if diff_x < 0:
        diff_x_unsigned = (1 << 16) + diff_x  # 转换为16位补码
    else:
        diff_x_unsigned = diff_x

    if diff_y < 0:
        diff_y_unsigned = (1 << 16) + diff_y  # 转换为16位补码
    else:
        diff_y_unsigned = diff_y

    # 限制在16位范围内
    diff_x_unsigned = diff_x_unsigned & 0xFFFF
    diff_y_unsigned = diff_y_unsigned & 0xFFFF

    # 分解为高低八位
    x_high = (diff_x_unsigned >> 8) & 0xFF  # x高八位
    x_low = diff_x_unsigned & 0xFF          # x低八位
    y_high = (diff_y_unsigned >> 8) & 0xFF  # y高八位
    y_low = diff_y_unsigned & 0xFF          # y低八位

    # 构建数据包
    packet = bytes([
        0x78,      # 包头
        0x01,      # 标识符（激光坐标差数据）
        x_high,    # x坐标差高八位
        x_low,     # x坐标差低八位
        y_high,    # y坐标差高八位
        y_low,     # y坐标差低八位
        0xFC       # 包尾
    ])

    # 通过串口发送二进制数据包（检查发送控制标志）
    global uart, send_enabled
    if not send_enabled:
        return  # 发送被禁用，直接返回

    if uart and uart.is_initialized:
        try:
            uart.serial.write(packet)  # 直接发送二进制数据
            print(f"发送数据包: diff_x={diff_x}, diff_y={diff_y}, 包=[{', '.join(f'0x{b:02X}' for b in packet)}]")
        except Exception as e:
            print(f"数据包发送失败: {e}")
    else:
        print("串口未初始化，无法发送数据包")

# --------------------------- 透视变换工具函数 ---------------------------
def perspective_transform(pts, target_width, target_height):
    """
    对四边形进行透视变换
    :param pts: 四边形顶点坐标 (4,2)
    :param target_width: 校正后宽度
    :param target_height: 校正后高度
    :return: 变换矩阵M和逆矩阵M_inv
    """
    # 顶点排序（左上→右上→右下→左下）
    s = pts.sum(axis=1)
    tl = pts[np.argmin(s)]
    br = pts[np.argmax(s)]
    diff = np.diff(pts, axis=1)
    tr = pts[np.argmin(diff)]
    bl = pts[np.argmax(diff)]
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
    
    # 目标坐标
    dst_pts = np.array([
        [0, 0], [target_width-1, 0],
        [target_width-1, target_height-1], [0, target_height-1]
    ], dtype=np.float32)
    
    # 计算变换矩阵
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
    ret, M_inv = cv2.invert(M)  # 逆矩阵用于映射回原图
    return M, M_inv, src_pts

# --------------------------- 全局控制变量 ---------------------------
send_enabled = True  # 数据发送控制标志，True=允许发送，False=停止发送

def check_control_commands():
    """检查串口接收到的控制指令"""
    global send_enabled, uart

    if uart and uart.serial:
        try:
            # 直接从串口读取原始字节数据，避免字符串转换
            available = uart.serial.available()  # 检查可用字节数
            if available > 0:
                raw_bytes = uart.serial.read(available)  # 读取指定数量的字节
                if raw_bytes:
                    print(f"收到原始字节: {[hex(b) for b in raw_bytes]}")  # 调试输出

                    # 直接检查字节值
                    for byte_val in raw_bytes:
                        print(f"检查字节: 0x{byte_val:02X}")  # 调试输出

                        if byte_val == 0xFF:  # 停止发送指令
                            send_enabled = False
                            print("收到停止发送指令 (0xFF)")
                        elif byte_val == 0x00:  # 开始发送指令
                            send_enabled = True
                            print("收到开始发送指令 (0x00)")

                    # 清空串口库的缓冲区，避免重复处理
                    uart.clear_buffer()
        except Exception as e:
            print(f"控制指令检查错误: {e}")

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    # 初始化设备
    disp = display.Display()
    cam = camera.Camera(180, 120, image.Format.FMT_BGR888)
    laser_detector = PurpleLaserDetector()

    # 初始化串口
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
        uart.set_frame("$$", "##", False)  # 禁用帧格式，直接处理原始字节
        print("串口设置为原始字节模式，支持二进制控制指令")
    else:
        print("串口初始化失败")
        exit()

    # 核心参数
    min_contour_area = 1000
    max_contour_area = 10000
    target_sides = 4
    
    # 透视变换与圆形参数
    corrected_width = 200    # 校正后矩形宽度
    corrected_height = 150   # 校正后矩形高度
    circle_radius = 40       # 校正后矩形内圆的半径
    circle_num_points = 12   # 圆周点数量
    
    # FPS计算初始化
    fps = 0
    last_time = time.ticks_ms()
    
    # 绘制参数（避免干扰识别区域）
    DRAW_PADDING = 10  # 绘制边距
    FPS_POSITION = (0, 10)  # FPS显示位置(右上)
    TEXT_FONT = cv2.FONT_HERSHEY_SIMPLEX
    TEXT_SCALE = 0.5
    TEXT_THICKNESS = 1
    TEXT_COLOR = (0, 255, 0)  # 绿色

    while not app.need_exit():
        # 检查串口控制指令
        check_control_commands()

        # 计算FPS
        current_time = time.ticks_ms()
        if current_time - last_time > 0:
            fps = 1000.0 / (current_time - last_time)
        last_time = current_time

        # 读取图像
        img = cam.read()
        img_cv = image.image2cv(img, ensure_bgr=True, copy=False)
        output = img_cv.copy()

        # 1. 矩形检测
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 132, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if min_contour_area < area < max_contour_area:
                epsilon = 0.03 * cv2.arcLength(cnt, True)
                approx = cv2.approxPolyDP(cnt, epsilon, True)
                if len(approx) == target_sides:
                    quads.append((approx, area))

        # 只保留最大的矩形
        inner_quads = []
        if quads:
            largest_quad = max(quads, key=lambda x: x[1])
            inner_quads = [largest_quad]

        # 2. 处理内框：透视变换→画圆→映射回原图
        all_circle_points = []  # 存储所有映射回原图的圆轨迹点
        for approx, area in inner_quads:
            # 提取顶点
            pts = approx.reshape(4, 2).astype(np.float32)
            
            # 计算透视变换矩阵
            M, M_inv, src_pts = perspective_transform(
                pts, corrected_width, corrected_height
            )
            
            # 生成校正后矩形内的圆形轨迹（圆心为校正后矩形的中心）
            corrected_center = (corrected_width//2, corrected_height//2)
            corrected_circle = generate_circle_points(
                corrected_center, circle_radius, circle_num_points
            )
            
            # 将校正后的圆轨迹点映射回原图
            if M_inv is not None:
                # 格式转换为opencv需要的形状 (1, N, 2)
                corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                original_points = [(int(x), int(y)) for x, y in original_points]
                all_circle_points.extend(original_points)
                
                # 绘制映射回原图的轨迹点（红色）
                for (x, y) in original_points:
                    cv2.circle(output, (x, y), 2, (0, 0, 255), -1)

        # 3. 激光检测（优先进行，避免被圆心绘制干扰）
        output, laser_points = laser_detector.detect(output)

        # 绘制内框轮廓和中心点（在激光检测之后）
        for approx, area in inner_quads:
            cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)
            M_moments = cv2.moments(approx)
            if M_moments["m00"] != 0:
                cx = int(M_moments["m10"] / M_moments["m00"])
                cy = int(M_moments["m01"] / M_moments["m00"])
                cv2.circle(output, (cx, cy), 5, (0, 255, 255), -1)  # 改为黄色，避免与紫色激光混淆

        # 4. 串口发送数据 - 发送激光点与圆心的坐标差
        if laser_points and all_circle_points:
            # 计算圆心坐标（取所有圆轨迹点的中心）
            if len(all_circle_points) > 0:
                circle_center_x = sum(x for x, y in all_circle_points) // len(all_circle_points)
                circle_center_y = sum(y for x, y in all_circle_points) // len(all_circle_points)

                # 对每个激光点发送与圆心的坐标差
                for laser_x, laser_y in laser_points:
                    diff_x = laser_x - circle_center_x  # 计算x坐标差
                    diff_y = laser_y - circle_center_y  # 计算y坐标差

                    # 发送二进制数据包：0x78, 标识符, x高八位, x低八位, y高八位, y低八位, 0xFC
                    send_laser_diff_packet(diff_x, diff_y)

        # 在右上角显示FPS和发送状态，避免干扰主要识别区域
        cv2.putText(output, f"FPS: {fps:.1f}", FPS_POSITION,
                   TEXT_FONT, TEXT_SCALE, TEXT_COLOR, TEXT_THICKNESS)

        # 显示数据发送状态
        send_status = "SEND: ON" if send_enabled else "SEND: OFF"
        send_color = (0, 255, 0) if send_enabled else (0, 0, 255)  # 绿色=开启，红色=关闭
        cv2.putText(output, send_status, (0, 25),
                   TEXT_FONT, TEXT_SCALE, send_color, TEXT_THICKNESS)

        # 显示图像
        img_show = image.cv2image(output, bgr=True, copy=False)
        disp.show(img_show)    